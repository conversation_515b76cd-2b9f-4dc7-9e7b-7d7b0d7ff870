using Godot;
using System.Collections.Generic;

public partial class ChestManager : Node
{
	public static ChestManager Instance { get; private set; }

	public override void _Ready()
	{
		if (Instance == null)
		{
			Instance = this;
			ConnectToResourcesManager();
		}
		else
		{
			QueueFree();
		}
	}

	private void ConnectToResourcesManager()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager != null)
		{
			resourcesManager.GameLoaded += OnGameLoaded;
		}
	}

	private void OnGameLoaded(object sender, System.EventArgs e)
	{
		LoadChests();
	}

	private void LoadChests()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		var chests = resourcesManager.GetChests();
		GD.Print($"ChestManager: Loading {chests.Count} chests");

		foreach (var chestData in chests)
		{
			if (!chestData.IsOpened)
			{
				LoadChest(chestData);
			}
		}
	}

	private void LoadChest(ChestSaveData chestData)
	{
		var chestScene = GD.Load<PackedScene>("res://scenes/mapObjects/Chest.tscn");
		if (chestScene == null)
		{
			GD.PrintErr("ChestManager: Could not load Chest.tscn");
			return;
		}

		var chest = chestScene.Instantiate<Chest>();
		if (chest == null)
		{
			GD.PrintErr("ChestManager: Failed to instantiate Chest");
			return;
		}

		chest.LoadFromSaveData(chestData);

		var world = GetNode("/root/world");
		if (world != null)
		{
			world.AddChild(chest);
			GD.Print($"ChestManager: Loaded chest at ({chestData.X}, {chestData.Y})");
		}
		else
		{
			GD.PrintErr("ChestManager: Could not find world node");
			chest.QueueFree();
		}
	}
}
