using Godot;

public partial class RegionUnlockManager : Node
{
	public static RegionUnlockManager Instance { get; private set; }

	public override void _Ready()
	{
		if (Instance == null)
		{
			Instance = this;
			ConnectSignals();
		}
		else
		{
			QueueFree();
		}
	}

	public override void _ExitTree()
	{
		DisconnectSignals();
	}

	private void ConnectSignals()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.BridgeBuilt += OnBridgeBuilt;
			CommonSignals.Instance.RabbitLegCooked += OnRabbitLegCooked;
		}
	}

	private void DisconnectSignals()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.BridgeBuilt -= OnBridgeBuilt;
			CommonSignals.Instance.RabbitLegCooked -= OnRabbitLegCooked;
		}
	}

	private void OnBridgeBuilt()
	{
		GD.Print("RegionUnlockManager: BridgeBuilt signal received!");
		if (!IsRegionUnlocked(2))
		{
			UnlockRegion(2);
			GD.Print("RegionUnlockManager: Region 2 unlocked - first bridge built!");
		}
		else
		{
			GD.Print("RegionUnlockManager: Region 2 already unlocked, ignoring bridge built signal");
		}
	}

	private void OnRabbitLegCooked()
	{
		if (!IsRegionUnlocked(3))
		{
			UnlockRegion(3);
			GD.Print("RegionUnlockManager: Region 3 unlocked - rabbit leg cooked!");
		}
	}

	private void UnlockRegion(int regionId)
	{
		// Emit the region unlock signal
		CommonSignals.Instance?.EmitRegionUnlocked(regionId);
		
		// Save the unlock status
		SaveUnlockStatus(regionId);
	}

	private void SaveUnlockStatus(int regionId)
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager?.GameData != null)
		{
			if (resourcesManager.GameData.UnlockedRegions == null)
			{
				resourcesManager.GameData.UnlockedRegions = new System.Collections.Generic.List<int>();
			}

			if (!resourcesManager.GameData.UnlockedRegions.Contains(regionId))
			{
				resourcesManager.GameData.UnlockedRegions.Add(regionId);
				GD.Print($"RegionUnlockManager: Saved region {regionId} unlock status");
			}
		}
	}

	public void UnlockRegionWithGold(int regionId)
	{
		if (CanUnlockRegion(regionId))
		{
			UnlockRegion(regionId);
			GD.Print($"RegionUnlockManager: Region {regionId} unlocked with gold!");
		}
		else
		{
			GD.Print($"RegionUnlockManager: Cannot unlock region {regionId} - either already unlocked or previous regions not unlocked");
		}
	}

	private bool IsRegionUnlocked(int regionId)
	{
		var resourcesManager = ResourcesManager.Instance;
		return resourcesManager?.GameData?.UnlockedRegions?.Contains(regionId) ?? false;
	}

	private bool CanUnlockRegion(int regionId)
	{
		if (IsRegionUnlocked(regionId))
			return false;

		if (regionId <= 3)
			return true;

		return IsRegionUnlocked(regionId - 1);
	}

	public int GetLastUnlockedRegion()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager?.GameData?.UnlockedRegions == null)
			return 1;

		var unlockedRegions = resourcesManager.GameData.UnlockedRegions;
		if (unlockedRegions.Count == 0)
			return 1;

		int maxRegion = 1;
		foreach (int region in unlockedRegions)
		{
			if (region > maxRegion)
				maxRegion = region;
		}

		return maxRegion;
	}
}
