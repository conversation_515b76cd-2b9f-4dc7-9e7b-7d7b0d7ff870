using Godot;

public partial class PlayerLight : Node2D
{
	[Export] public float LightRadius { get; set; } = 64.0f;
	[Export] public Color LightColor { get; set; } = Colors.White;
	[Export] public float LightEnergy { get; set; } = 1.0f;
	[Export] public bool OnlyShowAtNight { get; set; } = true;

	private Light2D _light;
	private DayNightManager _dayNightManager;

	public override void _Ready()
	{
		// Get the Light2D node from the scene
		_light = GetParent().GetNode<Light2D>("PlayerLight");
		if (_light == null)
		{
			GD.PrintErr("PlayerLight: Light2D node not found in scene!");
			return;
		}

		_light.Enabled = true;
		_light.Energy = LightEnergy;
		_light.Color = LightColor;
		_light.Scale = Vector2.One * (LightRadius / 64.0f); // Scale the light

		// Find the DayNightManager
		_dayNightManager = GetNode<DayNightManager>("/root/world/DayNight");
		if (_dayNightManager == null)
		{
			// Try alternative path
			_dayNightManager = GetTree().GetFirstNodeInGroup("DayNightManager") as DayNightManager;
			if (_dayNightManager == null)
			{
				GD.PrintErr("PlayerLight: DayNightManager not found!");
			}
		}

		GD.Print($"PlayerLight: Initialized with radius {LightRadius}");
	}

	public override void _Process(double delta)
	{
		if (_light == null || _dayNightManager == null) return;

		// Show/hide light based on day/night cycle
		if (OnlyShowAtNight)
		{
			_light.Visible = _dayNightManager.IsNight();
		}
		else
		{
			_light.Visible = true;
		}
	}

	public void SetLightRadius(float radius)
	{
		LightRadius = radius;
		if (_light != null)
		{
			_light.Scale = Vector2.One * (radius / 64.0f);
		}
		GD.Print($"PlayerLight: Radius set to {radius}");
	}

	public void SetLightColor(Color color)
	{
		LightColor = color;
		if (_light != null)
		{
			_light.Color = color;
		}
	}

	public void SetLightEnergy(float energy)
	{
		LightEnergy = energy;
		if (_light != null)
		{
			_light.Energy = energy;
		}
	}

	public void SetOnlyShowAtNight(bool nightOnly)
	{
		OnlyShowAtNight = nightOnly;
		GD.Print($"PlayerLight: OnlyShowAtNight set to {nightOnly}");
	}
}
