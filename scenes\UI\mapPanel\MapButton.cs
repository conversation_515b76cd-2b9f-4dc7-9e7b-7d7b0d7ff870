using Godot;

public partial class MapButton : Sprite2D
{
	private Button _button;
	private MapPanel _mapPanel;

	public override void _Ready()
	{
		_button = GetNode<Button>("Button");
		
		if (_button != null)
		{
			_button.Pressed += OnButtonPressed;
		}

		var currentScene = Engine.GetMainLoop() as SceneTree;
		_mapPanel = currentScene?.CurrentScene?.GetNode<MapPanel>("MapPanel");
		if (_mapPanel == null)
		{
			GD.PrintErr("MapButton: Could not find MapPanel in world scene");
		}
	}

	public override void _ExitTree()
	{
		if (_button != null)
		{
			_button.Pressed -= OnButtonPressed;
		}
	}

	private void OnButtonPressed()
	{
		if (_mapPanel != null)
		{
			_mapPanel.OpenPanel();
			GD.Print("MapButton: Opening map panel");
		}
		else
		{
			GD.PrintErr("MapButton: MapPanel reference is null");
		}
	}
}
