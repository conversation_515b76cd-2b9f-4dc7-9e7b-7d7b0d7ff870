using Godot;

public partial class DayNightManager : CanvasLayer
{
	[Export] public float DayDurationMinutes { get; set; } = 5.0f;
	[Export] public float NightDurationMinutes { get; set; } = 3.0f;
	[Export] public float NightDarknessLevel { get; set; } = 0.3f; // 0.0 = completely dark, 1.0 = no darkness
	[Export] public bool StartAtDawn { get; set; } = true;

	private Sprite2D _dayNightProgress;
	private Label _labelTime;
	private Label _labelAmPm;
	private CanvasModulate _canvasModulate;

	private float _currentTime = 0.0f; // 0.0 to 1.0 representing full day cycle
	private float _totalCycleDuration;
	private bool _isDay = true;

	// Time display
	private int _currentHour = 6; // Start at 6 AM
	private int _currentMinute = 0;
	private bool _isAM = true;

	public override void _Ready()
	{
		// Add to group for easy finding
		AddToGroup("DayNightManager");

		_dayNightProgress = GetNode<Sprite2D>("Control/DayNightProgress");
		_labelTime = GetNode<Label>("Control/LabelTime");
		_labelAmPm = GetNode<Label>("Control/LabelAmPm");

		// Create CanvasModulate for global lighting control
		_canvasModulate = new CanvasModulate();
		_canvasModulate.Color = Colors.White; // Start with full brightness
		CallDeferred(MethodName.AddCanvasModulate);

		// Calculate total cycle duration in seconds
		_totalCycleDuration = (DayDurationMinutes + NightDurationMinutes) * 60.0f;

		// Initialize time display
		UpdateTimeDisplay();
		UpdateDayNightVisuals();

		GD.Print($"DayNightManager: Initialized with {DayDurationMinutes}min day, {NightDurationMinutes}min night");
	}

	private void AddCanvasModulate()
	{
		GetTree().CurrentScene.AddChild(_canvasModulate);
		GD.Print("DayNightManager: CanvasModulate added to scene");
	}

	public override void _Process(double delta)
	{
		// Update cycle time
		_currentTime += (float)delta / _totalCycleDuration;
		if (_currentTime >= 1.0f)
		{
			_currentTime -= 1.0f; // Loop the cycle
		}

		// Update day/night state
		float dayPortion = DayDurationMinutes / (DayDurationMinutes + NightDurationMinutes);
		bool wasDay = _isDay;
		_isDay = _currentTime < dayPortion;

		// Log day/night transitions
		if (wasDay != _isDay)
		{
			GD.Print($"DayNightManager: Transitioned to {(_isDay ? "Day" : "Night")}");
		}

		// Update game time (12-hour format)
		UpdateGameTime(delta);

		// Update visuals
		UpdateDayNightVisuals();
		UpdateTimeDisplay();
	}

	private void UpdateGameTime(double delta)
	{
		// Game time progresses faster than real time
		// Full day cycle (24 hours) = DayDurationMinutes + NightDurationMinutes real minutes
		float gameTimeSpeed = 24.0f * 60.0f / _totalCycleDuration; // game minutes per real second
		float gameMinutesElapsed = (float)delta * gameTimeSpeed;

		_currentMinute += (int)gameMinutesElapsed;
		
		while (_currentMinute >= 60)
		{
			_currentMinute -= 60;
			_currentHour++;
			
			if (_currentHour > 12)
			{
				_currentHour = 1;
				_isAM = !_isAM;
			}
		}
	}

	private void UpdateDayNightVisuals()
	{
		// Update progress sprite frame (0-5 for different times)
		if (_dayNightProgress != null)
		{
			int frame = Mathf.FloorToInt(_currentTime * 6.0f);
			frame = Mathf.Clamp(frame, 0, 5);
			_dayNightProgress.Frame = frame;
		}

		// Update global lighting
		if (_canvasModulate != null)
		{
			if (_isDay)
			{
				// Day: full brightness
				_canvasModulate.Color = Colors.White;
			}
			else
			{
				// Night: reduced brightness based on darkness level
				float brightness = NightDarknessLevel;
				_canvasModulate.Color = new Color(brightness, brightness, brightness, 1.0f);
			}
		}
	}

	private void UpdateTimeDisplay()
	{
		if (_labelTime != null)
		{
			_labelTime.Text = $"{_currentHour:D2}:{_currentMinute:D2}";
		}

		if (_labelAmPm != null)
		{
			_labelAmPm.Text = _isAM ? "AM" : "PM";
		}
	}

	// Public methods for other systems
	public bool IsDay()
	{
		return _isDay;
	}

	public bool IsNight()
	{
		return !_isDay;
	}

	public float GetTimeOfDay()
	{
		return _currentTime;
	}

	public string GetCurrentTimeString()
	{
		return $"{_currentHour:D2}:{_currentMinute:D2} {(_isAM ? "AM" : "PM")}";
	}

	public void SetDayDuration(float minutes)
	{
		DayDurationMinutes = minutes;
		_totalCycleDuration = (DayDurationMinutes + NightDurationMinutes) * 60.0f;
		GD.Print($"DayNightManager: Day duration set to {minutes} minutes");
	}

	public void SetNightDuration(float minutes)
	{
		NightDurationMinutes = minutes;
		_totalCycleDuration = (DayDurationMinutes + NightDurationMinutes) * 60.0f;
		GD.Print($"DayNightManager: Night duration set to {minutes} minutes");
	}

	public void SetNightDarkness(float darkness)
	{
		NightDarknessLevel = Mathf.Clamp(darkness, 0.0f, 1.0f);
		GD.Print($"DayNightManager: Night darkness set to {darkness}");
	}

	// Skip to specific time for testing
	public void SkipToDay()
	{
		_currentTime = 0.1f; // Early day
		_currentHour = 6;
		_currentMinute = 0;
		_isAM = true;
		GD.Print("DayNightManager: Skipped to day");
	}

	public void SkipToNight()
	{
		float dayPortion = DayDurationMinutes / (DayDurationMinutes + NightDurationMinutes);
		_currentTime = dayPortion + 0.1f; // Early night
		_currentHour = 6;
		_currentMinute = 0;
		_isAM = false;
		GD.Print("DayNightManager: Skipped to night");
	}
}
