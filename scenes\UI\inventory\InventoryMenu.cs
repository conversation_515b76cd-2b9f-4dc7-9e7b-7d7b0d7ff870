using Godot;
using System;
using System.Collections.Generic;

public partial class InventoryMenu : CanvasLayer
{
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;
	private List<MenuSelectionSector> _selectionSectors = new List<MenuSelectionSector>();
	private MenuSelectionSectorTools _toolsSelector;
	private InventoryItemDescriptionPanel _descriptionPanel;

	private int _selectedSectorIndex = -1;
	private int _selectedSlotIndex = -1;
	private int _selectedToolSlot = -1;

	public event Action<ResourceType, int> ItemSelected;
	public event Action ItemDeselected;
	
	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_closeButton = GetNode<Button>("Control/Panel/CloseButton");

		var descriptionPanelNode = GetNode("Control/Panel/InventoryItemDescriptionPanel");
		if (descriptionPanelNode is InventoryItemDescriptionPanel panel)
		{
			_descriptionPanel = panel;
		}
		else
		{
			GD.PrintErr("InventoryItemDescriptionPanel script not attached or wrong type");
		}

		_closeButton.Pressed += OnCloseButtonPressed;

		InitializeSelectionSectors();
		InitializeToolsSelector();
		RefreshInventoryDisplay();

		// Listen to resource changes to refresh inventory
		if (ResourcesManager.Instance != null)
		{
			ResourcesManager.Instance.ResourceChanged += OnResourceChanged;
		}

		GetNode<Sprite2D>("Control/Panel").Visible = false;
	}

	public override void _ExitTree()
	{
		// Disconnect from resource changes
		if (ResourcesManager.Instance != null)
		{
			ResourcesManager.Instance.ResourceChanged -= OnResourceChanged;
		}
	}

	private void OnResourceChanged(object sender, ResourceChangedEventArgs e)
	{
		// Refresh inventory display when resources change
		RefreshInventoryDisplay();

		// If the currently selected item quantity changed to 0, clear selection
		if (_selectedSectorIndex >= 0 && _selectedSlotIndex >= 0)
		{
			var (selectedResourceType, quantity, hasSelection) = GetSelectedItem();
			if (hasSelection && selectedResourceType == e.ResourceType && e.NewValue == 0)
			{
				ClearAllSelections();
			}
		}
	}

	private void InitializeSelectionSectors()
	{
		var panel = GetNode<Sprite2D>("Control/Panel");

		foreach (Node child in panel.GetChildren())
		{
			if (child is MenuSelectionSector sector)
			{
				_selectionSectors.Add(sector);
				sector.ItemSelected += OnSectorItemSelected;
			}
		}

		_selectionSectors.Sort((a, b) => a.Name.ToString().CompareTo(b.Name.ToString()));

		GD.Print($"Initialized {_selectionSectors.Count} MenuSelectionSectors");
	}

	private void InitializeToolsSelector()
	{
		var inventoryTools = GetNode<Node2D>("Control/Panel/InventoryTools");
		if (inventoryTools != null)
		{
			var toolsSelectorNode = inventoryTools.GetNode("Panel/MenuSelectionSectorTools");
			if (toolsSelectorNode is MenuSelectionSectorTools toolsSelector)
			{
				_toolsSelector = toolsSelector;
				_toolsSelector.ToolSelected += OnToolSelected;
				GD.Print("Initialized MenuSelectionSectorTools");
			}
			else
			{
				GD.PrintErr("MenuSelectionSectorTools script not attached or wrong type");
			}
		}
		else
		{
			GD.PrintErr("InventoryTools not found");
		}
	}
	
	private void OnSectorItemSelected(MenuSelectionSector sector, int slotIndex, ResourceType resourceType, int quantity)
	{
		int sectorIndex = _selectionSectors.IndexOf(sector);

		HideAllPlaceholders();
		HideAllToolPlaceholders();

		_selectedSectorIndex = sectorIndex;
		_selectedSlotIndex = slotIndex;
		_selectedToolSlot = -1;

		_descriptionPanel?.SetSelectedResource(resourceType, quantity);

		ItemSelected?.Invoke(resourceType, quantity);

		GD.Print($"Selected item: {resourceType} (quantity: {quantity}) from sector {sectorIndex}, slot {slotIndex}");
	}

	private void OnToolSelected(MenuSelectionSectorTools toolsSelector, int slotIndex, ToolType toolType)
	{
		HideAllPlaceholders();
		HideAllToolPlaceholders();

		_selectedSectorIndex = -1;
		_selectedSlotIndex = -1;
		_selectedToolSlot = slotIndex;

		_descriptionPanel?.SetSelectedTool(toolType);

		GD.Print($"Selected tool: {toolType} from tools slot {slotIndex}");
	}

	private void HideAllPlaceholders()
	{
		foreach (var sector in _selectionSectors)
		{
			sector.HideAllPlaceholders();
		}
	}

	private void HideAllToolPlaceholders()
	{
		_toolsSelector?.HideAllPlaceholders();
	}

	private void ClearAllSelections()
	{
		HideAllPlaceholders();
		HideAllToolPlaceholders();

		_selectedSectorIndex = -1;
		_selectedSlotIndex = -1;
		_selectedToolSlot = -1;

		_descriptionPanel?.ClearSelection();

		ItemDeselected?.Invoke();
	}
	
	public void RefreshInventoryDisplay()
	{
		var rm = ResourcesManager.Instance;
		if (rm == null) return;

		var resources = rm.GetAllResources();
		var maxSlots = rm.GetMaxInventorySlots();

		int slotsPerSector = 4;
		int requiredSectors = Mathf.CeilToInt((float)maxSlots / slotsPerSector);

		for (int sectorIndex = 0; sectorIndex < _selectionSectors.Count; sectorIndex++)
		{
			var sector = _selectionSectors[sectorIndex];

			if (sectorIndex < requiredSectors)
			{
				sector.Visible = true;

				var sectorResources = new Dictionary<int, (ResourceType, int)>();

				int startSlot = sectorIndex * slotsPerSector;
				int resourceIndex = 0;

				foreach (var kvp in resources)
				{
					if (resourceIndex >= startSlot && resourceIndex < startSlot + slotsPerSector)
					{
						int slotInSector = resourceIndex - startSlot;
						sectorResources[slotInSector] = (kvp.Key, kvp.Value);
					}
					resourceIndex++;
				}

				sector.UpdateInventoryDisplay(sectorResources);
			}
			else
			{
				sector.Visible = false;
			}
		}
	}
	
	public void OpenInventory()
	{
		GetNode<Sprite2D>("Control/Panel").Visible = true;
		RefreshInventoryDisplay();
		_animationPlayer.Play("Open");

		// Disable player movement when inventory opens
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		// Show unassign elements in SelectedToolPanel
		var selectedToolPanel = GetNode<SelectedToolPanel>("/root/world/SelectedToolPanel");
		selectedToolPanel?.ShowUnassignElements();
	}

	public void CloseInventory()
	{
		ClearAllSelections();
		_animationPlayer.Play("Close");

		// Re-enable player movement when inventory closes
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);

		// Hide unassign elements in SelectedToolPanel
		var selectedToolPanel = GetNode<SelectedToolPanel>("/root/world/SelectedToolPanel");
		selectedToolPanel?.HideUnassignElements();
	}

	private void OnCloseButtonPressed()
	{
		CloseInventory();
	}

	public (ResourceType resourceType, int quantity, bool hasSelection) GetSelectedItem()
	{
		if (_selectedSectorIndex >= 0 && _selectedSlotIndex >= 0)
		{
			var sector = _selectionSectors[_selectedSectorIndex];
			return sector.GetSlotItem(_selectedSlotIndex);
		}

		return (ResourceType.Wood, 0, false);
	}

	public override void _Input(InputEvent @event)
	{
		if (@event is InputEventKey keyEvent && keyEvent.Pressed && keyEvent.Keycode == Key.Q)
		{
			if (!GetNode<Sprite2D>("Control/Panel").Visible)
			{
				OpenInventory();
			}
		}
	}
}
